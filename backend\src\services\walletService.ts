import UserWallet, { IUserWallet } from '../models/userWalletModel';
import Wallet from '../models/walletModel';
import DepositTransaction from '../models/depositTransactionModel';
import cryptoApiService from './cryptoApiService';
import crypto from 'crypto';
import { logger } from '../utils/logger';
import QRCode from 'qrcode';
import CryptoPriceHistory from '../models/cryptoPriceHistoryModel';
import mongoose from 'mongoose';

interface WalletBalance {
  currency: string;
  balance: number;
  usdtValue: number;
  lastUpdated: Date;
}

interface CreateWalletParams {
  userId: string;
  currency: string;
  network?: string;
}

class WalletService {
  private readonly SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];

  // Mock addresses for development/testing - in production, use proper wallet generation
  private readonly MOCK_ADDRESSES = {
    BTC: [
      '******************************************',
      '******************************************',
      '**************************************************************'
    ],
    ETH: [
      '******************************************',
      '0x8ba1f109551bD432803012645Hac136c22C4',
      '******************************************'
    ],
    USDT: [
      'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
      'TA0b86a33E6441b8435b662f98C94C6C4c4b4d8b6',
      'T742d35Cc6634C0532925a3b8D4C9db96C4b4d8b6'
    ],
    BNB: [
      'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2',
      'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
      'bnb136ns6lfw4zs5hg4n85vdthaad7hq5m4gtkgf23'
    ],
    ADA: [
      'addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh',
      'addr1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4',
      'addr1qrp33g0q5c5txsp9arysrx4k6zdkfs4nce4xj0gdcccefvpysxf3qccfmv3'
    ]
  };

  /**
   * Get all wallets for a user
   */
  async getUserWallets(userId: string): Promise<IUserWallet[]> {
    try {
      const wallets = await UserWallet.getUserWallets(userId);

      // If user has no wallets, create them
      if (wallets.length === 0) {
        return await this.createUserWallets(userId);
      }

      return wallets;
    } catch (error) {
      logger.error('Error getting user wallets:', error);
      throw new Error('Failed to get user wallets');
    }
  }

  /**
   * Create wallets for all supported currencies for a user
   */
  async createUserWallets(userId: string): Promise<IUserWallet[]> {
    try {
      const wallets: IUserWallet[] = [];

      for (const currency of this.SUPPORTED_CURRENCIES) {
        try {
          const wallet = await this.createWallet({ userId, currency });
          wallets.push(wallet);
        } catch (error) {
          logger.error(`Error creating ${currency} wallet for user ${userId}:`, error);
          // Continue with other currencies
        }
      }

      logger.info(`✅ Created ${wallets.length} wallets for user ${userId}`);
      return wallets;
    } catch (error) {
      logger.error('Error creating user wallets:', error);
      throw new Error('Failed to create user wallets');
    }
  }

  /**
   * Create a single wallet for a user
   */
  async createWallet(params: CreateWalletParams): Promise<IUserWallet> {
    try {
      const { userId, currency, network = 'mainnet' } = params;

      // Validate input parameters
      if (!userId || !currency) {
        throw new Error('UserId and currency are required');
      }

      if (!this.SUPPORTED_CURRENCIES.includes(currency.toUpperCase())) {
        throw new Error(`Unsupported currency: ${currency}`);
      }

      // Check if wallet already exists
      const existingWallets = await UserWallet.find({ userId, currency: currency.toUpperCase() });
      if (existingWallets.length > 0) {
        logger.info(`Wallet already exists for user ${userId} and currency ${currency}`);
        return existingWallets[0];
      }

      // Generate or get address for the currency
      const address = this.generateAddress(currency.toUpperCase());

      const wallet = await UserWallet.createUserWallet(userId, currency.toUpperCase(), address, network);

      logger.info(`✅ Created ${currency} wallet for user ${userId}: ${address}`);
      return wallet;
    } catch (error) {
      logger.error('Error creating wallet:', error);
      throw new Error(`Failed to create ${params.currency} wallet: ${error.message}`);
    }
  }

  /**
   * Get wallet balances with USDT values (Optimized version with aggregation)
   */
  async getWalletBalancesOptimized(userId: string): Promise<WalletBalance[]> {
    try {
      // Use MongoDB aggregation to join wallets with latest prices in a single query
      const pipeline = [
        // Match user wallets - convert userId to ObjectId if needed
        { $match: { userId: new mongoose.Types.ObjectId(userId) } },

        // Lookup latest prices for each currency
        {
          $lookup: {
            from: 'cryptopricehistories',
            let: { currency: '$currency' },
            pipeline: [
              { $match: { $expr: { $eq: ['$symbol', '$$currency'] } } },
              { $sort: { timestamp: -1 } },
              { $limit: 1 }
            ],
            as: 'priceData'
          }
        },

        // Add computed fields
        {
          $addFields: {
            latestPrice: { $arrayElemAt: ['$priceData.price', 0] },
            priceSource: { $arrayElemAt: ['$priceData.source', 0] },
            priceTimestamp: { $arrayElemAt: ['$priceData.timestamp', 0] }
          }
        },

        // Project final structure
        {
          $project: {
            currency: 1,
            balance: 1,
            lastUpdated: 1,
            latestPrice: 1,
            priceSource: 1,
            priceTimestamp: 1,
            usdtValue: {
              $cond: {
                if: { $eq: ['$currency', 'USDT'] },
                then: '$balance',
                else: {
                  $cond: {
                    if: { $and: ['$latestPrice', { $gt: ['$latestPrice', 0] }] },
                    then: { $multiply: ['$balance', '$latestPrice'] },
                    else: '$balance' // Will be handled by fallback logic
                  }
                }
              }
            }
          }
        }
      ];

      const results = await UserWallet.aggregate(pipeline as any);
      const balances: WalletBalance[] = [];

      const fallbackRates: { [key: string]: number } = {
        'BTC': 100000, 'ETH': 2500, 'BNB': 600, 'ADA': 0.4,
        'SOL': 150, 'DOGE': 0.15, 'TRX': 0.25, 'DOT': 8
      };

      for (const result of results) {
        let finalUsdtValue = result.usdtValue;

        // Handle fallback for currencies without price data
        if (result.currency !== 'USDT' && (!result.latestPrice || result.latestPrice <= 0)) {
          const fallbackRate = fallbackRates[result.currency] || 1;
          finalUsdtValue = result.balance * fallbackRate;
          logger.info(`Using fallback rate for ${result.currency}: $${fallbackRate}`);
        } else if (result.latestPrice) {
          logger.info(`Using database price for ${result.currency}: $${result.latestPrice}`);
        }

        balances.push({
          currency: result.currency,
          balance: result.balance,
          usdtValue: finalUsdtValue,
          lastUpdated: result.lastUpdated
        });
      }

      return balances;
    } catch (error) {
      logger.error('Error getting optimized wallet balances:', error);
      throw new Error('Failed to get wallet balances');
    }
  }

  /**
   * Get wallet balances with USDT values (Optimized with database fallback)
   */
  async getWalletBalances(userId: string): Promise<WalletBalance[]> {
    try {
      // Get wallets and prices in parallel for better performance
      const [wallets, latestPrices] = await Promise.all([
        this.getUserWallets(userId),
        CryptoPriceHistory.getAllLatestPrices()
      ]);

      const balances: WalletBalance[] = [];
      const fallbackRates: { [key: string]: number } = {
        'BTC': 100000,  // Updated to realistic 2025 price
        'ETH': 2500,    // Updated to realistic 2025 price
        'BNB': 600,     // Updated to realistic 2025 price
        'ADA': 0.4,     // Updated to realistic 2025 price
        'SOL': 150,     // Added SOL
        'DOGE': 0.15,   // Added DOGE
        'TRX': 0.25,    // Added TRX
        'DOT': 8
      };

      // Create price lookup map for O(1) access
      const priceMap = new Map<string, number>();
      latestPrices.forEach(price => {
        priceMap.set(price.symbol, price.price);
      });

      for (const wallet of wallets) {
        try {
          let usdtValue = wallet.balance;

          // Convert to USDT if not already USDT
          if (wallet.currency !== 'USDT' && wallet.balance > 0) {
            try {
              const conversion = await cryptoApiService.convertCurrency(
                wallet.balance,
                wallet.currency,
                'USDT'
              );
              usdtValue = conversion.amount;
            } catch (conversionError) {
              logger.warn(`Failed to convert ${wallet.currency} to USDT via API, trying database price`);

              // Use pre-fetched price data (O(1) lookup)
              const dbPrice = priceMap.get(wallet.currency);
              if (dbPrice && dbPrice > 0) {
                usdtValue = wallet.balance * dbPrice;
                logger.info(`Using database price for ${wallet.currency}: $${dbPrice}`);
              } else {
                // Fallback to updated rates
                const fallbackRate = fallbackRates[wallet.currency] || 1;
                usdtValue = wallet.balance * fallbackRate;
                logger.info(`Using fallback rate for ${wallet.currency}: $${fallbackRate}`);
              }
            }
          }

          balances.push({
            currency: wallet.currency,
            balance: wallet.balance,
            usdtValue,
            lastUpdated: wallet.lastUpdated
          });
        } catch (error) {
          logger.error(`Error processing balance for ${wallet.currency}:`, error);
          // Add with zero values if error
          balances.push({
            currency: wallet.currency,
            balance: 0,
            usdtValue: 0,
            lastUpdated: new Date()
          });
        }
      }

      return balances;
    } catch (error) {
      logger.error('Error getting wallet balances:', error);
      throw new Error('Failed to get wallet balances');
    }
  }

  /**
   * Get deposit history for a user
   */
  async getDepositHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const deposits = await DepositTransaction.getUserDeposits(userId);

      return deposits.slice(0, limit).map(deposit => ({
        id: deposit._id,
        currency: deposit.currency,
        amount: deposit.amount,
        usdtValue: deposit.usdtValue,
        status: deposit.status,
        confirmations: deposit.confirmations,
        requiredConfirmations: deposit.requiredConfirmations,
        transactionHash: deposit.transactionHash,
        createdAt: deposit.createdAt,
        confirmedAt: deposit.confirmedAt,
        autoInvestmentEnabled: deposit.autoInvestmentEnabled,
        investmentPackageId: deposit.investmentPackageId
      }));
    } catch (error) {
      logger.error('Error getting deposit history:', error);
      throw new Error('Failed to get deposit history');
    }
  }

  /**
   * Get wallet by address
   */
  async getWalletByAddress(address: string): Promise<IUserWallet | null> {
    try {
      return await UserWallet.getWalletByAddress(address);
    } catch (error) {
      logger.error('Error getting wallet by address:', error);
      return null;
    }
  }

  /**
   * Update wallet balance
   */
  async updateWalletBalance(address: string, newBalance: number): Promise<IUserWallet | null> {
    try {
      const wallet = await UserWallet.getWalletByAddress(address);
      if (!wallet) {
        throw new Error(`Wallet not found for address: ${address}`);
      }

      return await wallet.updateBalance(newBalance);
    } catch (error) {
      logger.error('Error updating wallet balance:', error);
      throw new Error('Failed to update wallet balance');
    }
  }

  /**
   * Update wallet interest balance for a specific asset
   */
  async updateInterestBalance(
    userId: string,
    currency: string,
    interestAmount: number,
    session?: any
  ): Promise<any> {
    try {
      const wallet = await Wallet.findOne({ userId }).session(session);

      if (!wallet) {
        throw new Error(`Wallet not found for user: ${userId}`);
      }

      // Find or create asset in wallet
      let asset = wallet.assets.find(a => a.symbol === currency.toUpperCase());

      if (!asset) {
        // Create new asset entry
        wallet.assets.push({
          symbol: currency.toUpperCase(),
          balance: 0,
          commissionBalance: 0,
          interestBalance: interestAmount,
          mode: 'interest'
        });
      } else {
        // Update existing asset
        asset.interestBalance = (asset.interestBalance || 0) + interestAmount;
      }

      // Update total interest earned
      wallet.totalInterestEarned = (wallet.totalInterestEarned || 0) + interestAmount;

      await wallet.save({ session });
      return wallet;
    } catch (error) {
      logger.error('Error updating wallet interest balance:', error);
      throw new Error('Failed to update wallet interest balance');
    }
  }

  /**
   * Get wallet interest balance for a specific asset
   */
  async getInterestBalance(userId: string, currency: string): Promise<number> {
    try {
      const wallet = await Wallet.findOne({ userId });

      if (!wallet) {
        return 0;
      }

      const asset = wallet.assets.find(a => a.symbol === currency.toUpperCase());
      return asset?.interestBalance || 0;
    } catch (error) {
      logger.error('Error getting wallet interest balance:', error);
      return 0;
    }
  }

  /**
   * Get total interest earned across all assets
   */
  async getTotalInterestEarned(userId: string): Promise<number> {
    try {
      const wallet = await Wallet.findOne({ userId });
      return wallet?.totalInterestEarned || 0;
    } catch (error) {
      logger.error('Error getting total interest earned:', error);
      return 0;
    }
  }

  /**
   * Generate QR code for wallet address using Node.js library
   */
  async generateQRCode(address: string, currency: string): Promise<string> {
    try {
      // Create QR code data with proper format for each currency
      let qrData = address;

      switch (currency.toUpperCase()) {
        case 'BTC':
          qrData = `bitcoin:${address}`;
          break;
        case 'ETH':
        case 'USDT':
          qrData = `ethereum:${address}`;
          break;
        case 'BNB':
          qrData = `binance:${address}`;
          break;
        case 'ADA':
          qrData = `cardano:${address}`;
          break;
        default:
          qrData = address;
      }

      // Generate QR code as base64 data URL
      const qrCodeDataURL = await QRCode.toDataURL(qrData, {
        type: 'image/png',
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',  // Black dots
          light: '#FFFFFF'  // White background
        },
        errorCorrectionLevel: 'M'
      });

      logger.info(`QR code generated for ${currency} address: ${address.substring(0, 10)}...`);
      return qrCodeDataURL;
    } catch (error) {
      logger.error('Error generating QR code:', error);

      // Fallback: generate simple QR code with just the address
      try {
        const fallbackQR = await QRCode.toDataURL(address, {
          type: 'image/png',
          width: 256,
          margin: 2,
          errorCorrectionLevel: 'L'
        });
        return fallbackQR;
      } catch (fallbackError) {
        logger.error('Fallback QR code generation failed:', fallbackError);
        throw new Error('Failed to generate QR code');
      }
    }
  }

  /**
   * Generate QR code as SVG format
   */
  async generateQRCodeSVG(address: string, currency: string): Promise<string> {
    try {
      // Create QR code data with proper format for each currency
      let qrData = address;

      switch (currency.toUpperCase()) {
        case 'BTC':
          qrData = `bitcoin:${address}`;
          break;
        case 'ETH':
        case 'USDT':
          qrData = `ethereum:${address}`;
          break;
        case 'BNB':
          qrData = `binance:${address}`;
          break;
        case 'ADA':
          qrData = `cardano:${address}`;
          break;
        default:
          qrData = address;
      }

      // Generate QR code as SVG
      const qrCodeSVG = await QRCode.toString(qrData, {
        type: 'svg',
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      });

      logger.info(`QR code SVG generated for ${currency} address: ${address.substring(0, 10)}...`);
      return qrCodeSVG;
    } catch (error) {
      logger.error('Error generating QR code SVG:', error);
      throw new Error('Failed to generate QR code SVG');
    }
  }

  /**
   * Generate QR code with custom options
   */
  async generateCustomQRCode(
    address: string,
    currency: string,
    options: {
      size?: number;
      format?: 'png' | 'svg';
      darkColor?: string;
      lightColor?: string;
      margin?: number;
      errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
    } = {}
  ): Promise<string> {
    try {
      const {
        size = 256,
        format = 'png',
        darkColor = '#000000',
        lightColor = '#FFFFFF',
        margin = 2,
        errorCorrectionLevel = 'M'
      } = options;

      // Create QR code data with proper format for each currency
      let qrData = address;

      switch (currency.toUpperCase()) {
        case 'BTC':
          qrData = `bitcoin:${address}`;
          break;
        case 'ETH':
        case 'USDT':
          qrData = `ethereum:${address}`;
          break;
        case 'BNB':
          qrData = `binance:${address}`;
          break;
        case 'ADA':
          qrData = `cardano:${address}`;
          break;
        default:
          qrData = address;
      }

      let qrCode: string;
      if (format === 'svg') {
        const svgOptions = {
          type: 'svg' as const,
          width: size,
          margin,
          color: {
            dark: darkColor,
            light: lightColor
          },
          errorCorrectionLevel
        };
        qrCode = await QRCode.toString(qrData, svgOptions);
      } else {
        const pngOptions = {
          type: 'image/png' as const,
          width: size,
          margin,
          color: {
            dark: darkColor,
            light: lightColor
          },
          errorCorrectionLevel
        };
        qrCode = await QRCode.toDataURL(qrData, pngOptions);
      }

      logger.info(`Custom QR code generated for ${currency} address (${format}, ${size}px)`);
      return qrCode;
    } catch (error) {
      logger.error('Error generating custom QR code:', error);
      throw new Error('Failed to generate custom QR code');
    }
  }

  /**
   * Generate address for a currency
   * In production, this should use proper wallet generation libraries
   */
  private generateAddress(currency: string): string {
    try {
      // Check if we're in production mode
      const isProduction = process.env.NODE_ENV === 'production';

      if (isProduction) {
        // In production, generate real addresses using proper libraries
        return this.generateProductionAddress(currency);
      } else {
        // In development, use mock addresses
        const addresses = this.MOCK_ADDRESSES[currency as keyof typeof this.MOCK_ADDRESSES];
        if (!addresses || addresses.length === 0) {
          throw new Error(`No mock addresses available for ${currency}`);
        }

        // Return a random address from the mock list
        const randomIndex = Math.floor(Math.random() * addresses.length);
        return addresses[randomIndex];
      }
    } catch (error) {
      logger.error(`Error generating address for ${currency}:`, error);
      // Fallback to a generic address
      const isProduction = process.env.NODE_ENV === 'production';
      return `${isProduction ? 'prod' : 'mock'}_${currency.toLowerCase()}_${crypto.randomBytes(16).toString('hex')}`;
    }
  }

  /**
   * Generate production wallet address
   * This is a placeholder - implement with proper wallet generation libraries
   */
  private generateProductionAddress(currency: string): string {
    // TODO: Implement proper wallet generation using libraries like:
    // - bitcoinjs-lib for Bitcoin
    // - ethers.js for Ethereum
    // - @solana/web3.js for Solana
    // - cardano-serialization-lib for Cardano

    logger.warn(`Production address generation not implemented for ${currency}. Using placeholder.`);

    // For now, return a placeholder that follows the format
    switch (currency.toUpperCase()) {
      case 'BTC':
        return `bc1q${crypto.randomBytes(32).toString('hex').substring(0, 39)}`;
      case 'ETH':
      case 'USDT':
        return `0x${crypto.randomBytes(20).toString('hex')}`;
      case 'BNB':
        return `bnb1${crypto.randomBytes(20).toString('hex').substring(0, 38)}`;
      case 'ADA':
        return `addr1q${crypto.randomBytes(28).toString('hex').substring(0, 50)}`;
      default:
        return `${currency.toLowerCase()}_${crypto.randomBytes(20).toString('hex')}`;
    }
  }

  /**
   * Get total deposits summary for a user
   */
  async getDepositsSummary(userId: string): Promise<any> {
    try {
      const totalDeposits = await DepositTransaction.getTotalDepositsByUser(userId);

      const summary = {
        totalDeposits: 0,
        totalUSDTValue: 0,
        currencyBreakdown: totalDeposits,
        pendingDeposits: 0,
        confirmedDeposits: 0
      };

      // Calculate totals
      for (const deposit of totalDeposits) {
        summary.totalUSDTValue += deposit.totalUSDTValue || 0;
        summary.confirmedDeposits += deposit.count;
      }

      // Get pending deposits count
      const pendingDeposits = await DepositTransaction.find({
        userId,
        status: 'pending'
      }).countDocuments();

      summary.pendingDeposits = pendingDeposits;

      return summary;
    } catch (error) {
      logger.error('Error getting deposits summary:', error);
      throw new Error('Failed to get deposits summary');
    }
  }
}

// Export singleton instance
const walletService = new WalletService();
export default walletService;
